# Soremo MediaConvert Lambda - Architecture Overview

## 🌟 Simple Overview

### How the System Works in Simple Terms

```mermaid
flowchart TD
    %% Input
    USER["👤 User<br/>Uploads audio/video file"]
    
    %% Storage
    S3_IN["☁️ AWS S3 Input<br/>Stores original files"]
    
    %% Processing
    LAMBDA["🔧 AWS Lambda<br/>Automatic file processor"]
    
    %% Decision
    CHECK{"📋 Check file type"}
    
    %% Audio Path
    AUDIO["🎵 Audio Processing<br/>Create waveform<br/>(5 seconds)"]
    
    %% Video Path  
    VIDEO["🎬 Video Processing<br/>Convert to HLS format<br/>(30-60 seconds)"]
    
    %% Output Storage
    S3_OUT["☁️ AWS S3 Output<br/>Stores processed files"]
    
    %% Database
    DB["🗄️ Database<br/>Records processing status"]
    
    %% Frontend
    FRONTEND["📱 Website/App<br/>Displays to users"]
    
    %% Flow connections
    USER --> S3_IN
    S3_IN --> LAMBDA
    LAMBDA --> CHECK
    
    CHECK -->|"Audio files<br/>MP3, WAV, etc."| AUDIO
    CHECK -->|"Video files<br/>MP4, MOV, etc."| VIDEO
    
    AUDIO --> S3_OUT
    VIDEO --> S3_OUT
    
    AUDIO --> DB
    VIDEO --> DB
    
    S3_OUT --> FRONTEND
    
    %% Styling
    classDef user fill:#e3f2fd,stroke:#1976d2,stroke-width:3px
    classDef aws fill:#ff9800,stroke:#f57c00,stroke-width:2px
    classDef process fill:#4caf50,stroke:#388e3c,stroke-width:2px
    classDef storage fill:#2196f3,stroke:#1976d2,stroke-width:2px
    classDef frontend fill:#9c27b0,stroke:#7b1fa2,stroke-width:2px
    
    class USER user
    class S3_IN,S3_OUT,LAMBDA aws
    class AUDIO,VIDEO process
    class DB,CHECK storage
    class FRONTEND frontend
```

### 🔄 Step-by-Step Process

#### **Step 1: File Upload** 👤 → ☁️
- User uploads an audio or video file through the website
- File is automatically stored in **AWS S3** (cloud storage service)

#### **Step 2: Automatic Processing** ☁️ → 🔧
- **AWS Lambda** (automated processing service) detects the new file
- Lambda automatically starts processing the file

#### **Step 3: File Type Detection** 🔧 → 📋
- System checks whether the file is audio or video
- Different processing methods are used based on file type

#### **Step 4a: Audio Processing** 🎵
- **For audio files** (MP3, WAV, M4A, AAC, FLAC, OGG):
- Creates waveform peaks for visualization (like the sound waves you see on Spotify)
- **Very fast**: Takes only ~5 seconds

#### **Step 4b: Video Processing** 🎬
- **For video files** (MP4, MOV, MKV, AVI, WebM, MPG):
- Converts to HLS format for web streaming
- **Slower process**: Takes 30-60 seconds depending on file size

#### **Step 5: Store Results** 📁
- Processed files are saved to **S3 Output**
- Processing status is recorded in **Database**

#### **Step 6: Display to Users** 📱
- Website/App retrieves processed files from S3
- Users can now view/listen to their media

---

### ⚡ **System Benefits**
- **100% Automatic**: No manual intervention required
- **Fast**: Audio processing in 5 seconds
- **Cost-Effective**: Pay only when processing files
- **Reliable**: AWS ensures 24/7 operation
- **Scalable**: Handles multiple files simultaneously

### 💰 **Main AWS Costs**
- **S3 Storage**: File storage (~few dollars/month)
- **Lambda**: File processing (~few cents/1000 files)
- **MediaConvert**: Video conversion (~$0.0225/minute of video)
- **Database**: Status tracking (~minimal cost)

### 📊 **Processing Performance**

| File Type | Processing Time | Output Format | Cost Level |
|-----------|----------------|---------------|------------|
| **Audio** | ~5 seconds | JSON waveform peaks | Very Low |
| **Video** | ~30-60 seconds | HLS (streaming format) | Moderate |

### 🎯 **What Users Get**
- **Audio Files**: Interactive waveform visualization
- **Video Files**: Smooth streaming playback on any device
- **Fast Loading**: Optimized formats for web delivery
- **Cross-Platform**: Works on desktop, mobile, and tablets

---

## 🏗️ System Architecture (v2.0.0)

### Main Architecture Diagram

```mermaid
graph TB
    %% Input Sources
    S3_INPUT["📁 S3 Input Bucket<br/>Audio/Video Files"]

    %% Lambda Functions
    HANDLER1["🔄 Handler 1<br/>startConversionHandler<br/>Process S3 Events"]
    HANDLER2["🔄 Handler 2<br/>handleCompletionHandler<br/>Process EventBridge Events"]

    %% Decision Point
    DECISION{"📋 File Type<br/>Detection"}

    %% Audio Processing Path
    AUDIO_PROCESS["🎵 Audio Processing<br/>Direct audiowaveform<br/>Peak Generation"]
    AUDIO_PEAKS["📊 Generate Peaks<br/>JSON Format<br/>32 min/max pairs"]

    %% Video Processing Path
    VIDEO_MC["🎬 AWS MediaConvert<br/>HLS Conversion<br/>Job Creation"]
    EVENTBRIDGE["⚡ EventBridge<br/>Job Completion<br/>Trigger"]

    %% Storage
    S3_OUTPUT["📁 S3 Output Bucket<br/>Peaks & HLS Files"]

    %% Database
    RDS["🗄️ MySQL Database<br/>Job Status Tracking"]
    SSM["🔐 AWS SSM<br/>Database Credentials"]

    %% External Services
    AUDIOWAVEFORM["🔊 audiowaveform Binary<br/>High-Performance<br/>Waveform Generation"]

    %% Flow Connections
    S3_INPUT -->|"S3 Event Trigger"| HANDLER1
    HANDLER1 --> DECISION

    %% Audio Path
    DECISION -->|"Audio File<br/>MP3, WAV, M4A<br/>AAC, FLAC, OGG"| AUDIO_PROCESS
    AUDIO_PROCESS --> AUDIOWAVEFORM
    AUDIOWAVEFORM --> AUDIO_PEAKS
    AUDIO_PEAKS --> S3_OUTPUT
    AUDIO_PROCESS --> RDS

    %% Video Path
    DECISION -->|"Video File<br/>MP4, MOV, MKV<br/>AVI, WebM, MPG"| VIDEO_MC
    VIDEO_MC --> EVENTBRIDGE
    EVENTBRIDGE -->|"Job Complete/Error"| HANDLER2
    HANDLER2 --> RDS
    VIDEO_MC --> S3_OUTPUT

    %% Database Connections
    HANDLER1 -.->|"Get Credentials"| SSM
    HANDLER2 -.->|"Get Credentials"| SSM
    SSM -.->|"DB Config"| RDS

    %% Styling
    classDef inputOutput fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef lambda fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef aws fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef processing fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef decision fill:#fff8e1,stroke:#f57f17,stroke-width:2px

    class S3_INPUT,S3_OUTPUT inputOutput
    class HANDLER1,HANDLER2 lambda
    class VIDEO_MC,EVENTBRIDGE,RDS,SSM aws
    class AUDIO_PROCESS,AUDIO_PEAKS,AUDIOWAVEFORM processing
    class DECISION decision
```

## 🎵 Audio Processing Flow

```mermaid
sequenceDiagram
    participant User
    participant S3_Input as S3 Input Bucket
    participant Lambda1 as "Handler 1<br/>(startConversionHandler)"
    participant Audiowaveform as audiowaveform Binary
    participant S3_Output as S3 Output Bucket
    participant SSM as AWS SSM
    participant RDS as MySQL Database

    User->>S3_Input: "1. Upload audio file<br/>(MP3, WAV, M4A, etc.)"
    S3_Input->>Lambda1: "2. S3 Event Trigger"

    Lambda1->>Lambda1: "3. Detect file type<br/>(Audio detected)"
    Lambda1->>S3_Input: "4. Download audio file"

    Lambda1->>SSM: "5. Get DB credentials"
    SSM-->>Lambda1: "6. Return credentials"

    Lambda1->>Audiowaveform: "7. Generate waveform<br/>audiowaveform -i input -o output<br/>--width 32 --output-format json"
    Audiowaveform-->>Lambda1: "8. Raw waveform JSON"

    Lambda1->>Lambda1: "9. Process peaks data<br/>- Parse JSON<br/>- Normalize values (-1 to 1)<br/>- Create min/max pairs<br/>- Format for wavesurfer.js"

    Lambda1->>S3_Output: "10. Upload peaks JSON<br/>file_peaks.json"

    Lambda1->>RDS: "11. Update database<br/>Status: COMPLETE<br/>Waveform key: file_peaks.json"

    Lambda1-->>S3_Input: "12. Processing complete<br/>(~5 seconds total)"

    Note over User,RDS: "Audio files are processed directly<br/>No MediaConvert needed<br/>Fast and cost-effective"
```

## 🎬 Video Processing Flow

```mermaid
sequenceDiagram
    participant User
    participant S3_Input as S3 Input Bucket
    participant Lambda1 as "Handler 1<br/>(startConversionHandler)"
    participant MediaConvert as AWS MediaConvert
    participant EventBridge as AWS EventBridge
    participant Lambda2 as "Handler 2<br/>(handleCompletionHandler)"
    participant S3_Output as S3 Output Bucket
    participant SSM as AWS SSM
    participant RDS as MySQL Database

    User->>S3_Input: "1. Upload video file<br/>(MP4, MOV, MKV, etc.)"
    S3_Input->>Lambda1: "2. S3 Event Trigger"

    Lambda1->>Lambda1: "3. Detect file type<br/>(Video detected)"

    Lambda1->>SSM: "4. Get DB credentials"
    SSM-->>Lambda1: "5. Return credentials"

    Lambda1->>MediaConvert: "6. Create MediaConvert job<br/>- Input: S3 source file<br/>- Output: HLS format<br/>- Destination: S3 output bucket"
    MediaConvert-->>Lambda1: "7. Job ID returned"

    Lambda1->>RDS: "8. Update database<br/>Status: PROCESSING<br/>Job ID: job-12345"

    Lambda1-->>S3_Input: "9. Job submitted<br/>(Handler 1 complete)"

    Note over MediaConvert: "MediaConvert processes video<br/>Converts to HLS format<br/>(30-60 seconds)"

    MediaConvert->>S3_Output: "10. Upload HLS files<br/>- .m3u8 playlist<br/>- .ts segments"

    MediaConvert->>EventBridge: "11. Job completion event<br/>Status: COMPLETE/ERROR"

    EventBridge->>Lambda2: "12. Trigger Handler 2"

    Lambda2->>SSM: "13. Get DB credentials"
    SSM-->>Lambda2: "14. Return credentials"

    alt Job Successful
        Lambda2->>RDS: "15a. Update database<br/>Status: COMPLETE<br/>Output key: file-converted.m3u8"
    else Job Failed
        Lambda2->>RDS: "15b. Update database<br/>Status: FAILED<br/>Error message: details"
    end

    Lambda2-->>EventBridge: "16. Processing complete"

    Note over User,RDS: "Video files use MediaConvert<br/>Only creates HLS output<br/>No waveform peaks needed"
```

## 📊 Handler Overview

| Handler | Trigger | Purpose | Audio Output | Video Output |
|---------|---------|---------|-------------|--------------|
| `startConversionHandler` | S3 Upload | File processing & job creation | `COMPLETE` (with peaks) | `PROCESSING` (job created) |
| `handleCompletionHandler` | EventBridge | Update status after video conversion | N/A | `COMPLETE` or `FAILED` |

## 🔧 Technical Architecture & Components

```mermaid
graph TB
    subgraph "🌐 Frontend Integration"
        FRONTEND["📱 Frontend Application<br/>React/Vue/Angular"]
        WAVESURFER["🎵 wavesurfer.js<br/>Audio Visualization"]
        HLS_PLAYER["📺 HLS Video Player<br/>Video.js/Plyr"]
    end

    subgraph "☁️ AWS Cloud Infrastructure"
        subgraph "📦 Storage Layer"
            S3_IN["📁 S3 Input Bucket<br/>Raw Media Files"]
            S3_OUT["📁 S3 Output Bucket<br/>Processed Files"]
        end

        subgraph "⚡ Compute Layer"
            LAMBDA_CONTAINER["🐳 Lambda Container<br/>Node.js 20 Runtime<br/>audiowaveform Binary"]

            subgraph "Lambda Functions"
                HANDLER1["🔄 startConversionHandler<br/>File Processing Logic"]
                HANDLER2["🔄 handleCompletionHandler<br/>Job Status Updates"]
            end
        end

        subgraph "🎬 Media Processing"
            MEDIACONVERT["🎥 AWS MediaConvert<br/>Video → HLS Conversion"]
            AUDIOWAVEFORM["🔊 audiowaveform Binary<br/>Audio → Peaks Generation"]
        end

        subgraph "📊 Data & Events"
            RDS["🗄️ Amazon RDS MySQL<br/>Job Status & Metadata"]
            SSM["🔐 AWS Systems Manager<br/>Parameter Store<br/>Database Credentials"]
            EVENTBRIDGE["⚡ Amazon EventBridge<br/>MediaConvert Events"]
            CLOUDWATCH["📈 CloudWatch<br/>Logs & Monitoring"]
        end
    end

    subgraph "🔧 Development & Deployment"
        DOCKER["🐳 Docker Multi-stage Build<br/>audiowaveform Compilation<br/>Lambda Runtime"]
        ECR["📦 Amazon ECR<br/>Container Registry"]
        DEPLOY["🚀 Deployment Script<br/>./deploy.sh"]
    end

    %% Frontend Connections
    FRONTEND --> S3_OUT
    FRONTEND --> WAVESURFER
    FRONTEND --> HLS_PLAYER
    WAVESURFER -.->|"Load Peaks JSON"| S3_OUT
    HLS_PLAYER -.->|"Stream HLS"| S3_OUT

    %% Processing Flow
    S3_IN -->|"Trigger"| HANDLER1
    HANDLER1 --> AUDIOWAVEFORM
    HANDLER1 --> MEDIACONVERT
    AUDIOWAVEFORM --> S3_OUT
    MEDIACONVERT --> S3_OUT
    MEDIACONVERT --> EVENTBRIDGE
    EVENTBRIDGE --> HANDLER2

    %% Data Flow
    HANDLER1 --> RDS
    HANDLER2 --> RDS
    HANDLER1 -.-> SSM
    HANDLER2 -.-> SSM

    %% Monitoring
    LAMBDA_CONTAINER --> CLOUDWATCH
    MEDIACONVERT --> CLOUDWATCH

    %% Deployment
    DOCKER --> ECR
    ECR --> LAMBDA_CONTAINER
    DEPLOY --> ECR

    %% Styling
    classDef frontend fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef aws fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef storage fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef compute fill:#fce4ec,stroke:#c2185b,stroke-width:2px
    classDef data fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef deploy fill:#fff8e1,stroke:#fbc02d,stroke-width:2px

    class FRONTEND,WAVESURFER,HLS_PLAYER frontend
    class MEDIACONVERT,EVENTBRIDGE,CLOUDWATCH aws
    class S3_IN,S3_OUT storage
    class LAMBDA_CONTAINER,HANDLER1,HANDLER2,AUDIOWAVEFORM compute
    class RDS,SSM data
    class DOCKER,ECR,DEPLOY deploy
```

## 🔧 Technical Implementation Details

### Handler 1: startConversionHandler
```javascript
/**
 * Main handler for processing uploaded media files
 * - Audio files: Generate waveform peaks directly using audiowaveform
 * - Video files: Create MediaConvert job for HLS conversion
 * Triggered by S3 upload events
 */
exports.startConversionHandler = async(event, context) => {
    // File type detection
    const extension = path.extname(sourceKey).toLowerCase();
    let fileType = 'unknown';
    if (VIDEO_EXTENSIONS.includes(extension)) {
        fileType = 'video';
    } else if (AUDIO_EXTENSIONS.includes(extension)) {
        fileType = 'audio';
    }

    if (fileType === 'audio') {
        // Direct audio processing with audiowaveform
        await generateWaveformPeaksFromAudiowaveform(localInput, localJsonOutput, 32, 'json');
        await s3.upload({ Bucket: OUTPUT_BUCKET, Key: wavesurferJsonS3Key, Body: fs.readFileSync(localJsonOutput) }).promise();
        await connection.execute('INSERT INTO app_mediaconvertjob ... VALUES (?, ?, ?, ?, ?)', [sourceKey, 'COMPLETE', wavesurferJsonS3Key, now, now]);
        return { statusCode: 200, body: 'Audio file processed directly' };
    } else if (fileType === 'video') {
        // MediaConvert job creation
        const mcResponse = await mediaConvert.createJob({ Role: MEDIA_CONVERT_ROLE_ARN, Settings: jobSettings }).promise();
        await connection.execute('INSERT INTO app_mediaconvertjob ... VALUES (?, ?, ?, ?, ?)', [sourceKey, 'PROCESSING', jobId, now, now]);
        return { statusCode: 200, body: 'Job created successfully' };
    }
};
```

### Handler 2: handleCompletionHandler
```javascript
/**
 * Handler for MediaConvert job completion events
 * Updates database with job results for video files
 * Triggered by EventBridge when MediaConvert jobs complete
 */
exports.handleCompletionHandler = async(event, context) => {
    const jobStatus = event.detail.status;
    const originalKey = event.detail.userMetadata.original_object_key;

    if (jobStatus === 'COMPLETE') {
        const outputKey = event.detail.outputGroupDetails[0].outputDetails[0].outputFilePaths[0];
        await connection.execute('UPDATE app_mediaconvertjob SET status = ?, converted_media_key = ? WHERE original_object_key = ?', ['COMPLETE', outputKey, originalKey]);
    } else if (jobStatus === 'ERROR') {
        const errorMessage = event.detail.errorMessage || 'Unknown error';
        await connection.execute('UPDATE app_mediaconvertjob SET status = ?, error_message = ? WHERE original_object_key = ?', ['FAILED', errorMessage, originalKey]);
    }
};
```

## 🚀 Key Benefits & Performance

### 📈 Performance Metrics
| Metric | Audio Files | Video Files |
|--------|-------------|-------------|
| **Processing Time** | ~5 seconds | ~30-60 seconds |
| **Output Format** | JSON peaks | HLS (m3u8 + ts) |
| **AWS Services** | Lambda only | Lambda + MediaConvert |
| **Cost** | Very low | Moderate |

### 🎯 Architecture Benefits
- **Direct Audio Processing**: No MediaConvert overhead for audio files
- **Optimized Tool**: audiowaveform is faster than FFmpeg for waveform generation  
- **Simplified Flow**: Only 2 handlers needed
- **Cost Effective**: Reduced MediaConvert usage for audio files
- **Better UX**: Faster processing means better user experience

### 🔧 Technical Advantages
- **Single Container**: Both handlers in one Lambda container
- **Shared Dependencies**: Efficient resource utilization
- **Modern Runtime**: Node.js 20 with latest features
- **Better Monitoring**: Centralized logging and metrics

## 📦 AWS Resources & Configuration

### Required AWS Services
| Service | Purpose | Configuration |
|---------|---------|---------------|
| **Lambda** | Container runtime | Node.js 20, 3008MB memory, 15min timeout |
| **S3 Input Bucket** | Raw media files | Event trigger → `startConversionHandler` |
| **S3 Output Bucket** | Processed files | Store peaks JSON and HLS files |
| **EventBridge** | MediaConvert events | Trigger `handleCompletionHandler` |
| **MediaConvert** | Video conversion | HLS output, only for video files |
| **RDS MySQL** | Job tracking | Status, metadata, error handling |
| **SSM Parameter Store** | Database credentials | Encrypted parameter storage |
| **ECR** | Container registry | Store Lambda container images |

### Environment Variables
```bash
# Required Lambda environment variables
OUTPUT_BUCKET=your-output-bucket-name
MEDIA_CONVERT_ROLE_ARN=arn:aws:iam::account:role/MediaConvertRole
MEDIA_CONVERT_ENDPOINT=https://mediaconvert.region.amazonaws.com
RDS_PARAM_NAME=/soremo/database/credentials
```

## 🔄 Deployment

### Docker Build Process
```bash
# Multi-stage Docker build
# Stage 1: Build audiowaveform from source
FROM amazonlinux:2023 as builder
RUN dnf install -yq gcc gcc-c++ cmake3 wget tar gzip
# ... build audiowaveform with all dependencies

# Stage 2: Lambda runtime with audiowaveform binary
FROM public.ecr.aws/lambda/nodejs:20
COPY --from=builder /tmp/audiowaveform-1.10.1/build/audiowaveform /usr/local/bin/
COPY package*.json ./
RUN npm ci --only=production
COPY index.js ./
CMD [ "index.startConversionHandler" ]
```

### Deployment Steps
```bash
# 1. Build and push container
./deploy.sh push

# 2. Update Lambda function
aws lambda update-function-code \
  --function-name soremo-mediaconvert \
  --image-uri account.dkr.ecr.region.amazonaws.com/soremo-mediaconvert-lambda:latest

# 3. Configure environment variables
aws lambda update-function-configuration \
  --function-name soremo-mediaconvert \
  --environment Variables='{
    "OUTPUT_BUCKET":"your-bucket",
    "MEDIA_CONVERT_ROLE_ARN":"your-role-arn",
    "MEDIA_CONVERT_ENDPOINT":"your-endpoint",
    "RDS_PARAM_NAME":"your-param-name"
  }'
```

## 🧪 Testing & Validation

### Audio File Testing
```bash
# 1. Upload test audio file
aws s3 cp test-audio.wav s3://your-input-bucket/test/audio.wav

# 2. Monitor Lambda execution
aws logs tail /aws/lambda/soremo-mediaconvert --follow

# 3. Verify peaks JSON file
aws s3 ls s3://your-output-bucket/test/audio_peaks.json
aws s3 cp s3://your-output-bucket/test/audio_peaks.json ./

# 4. Check database record
mysql -h your-rds-host -u username -p -e "
  SELECT original_object_key, status, waveform_data_key, created
  FROM app_mediaconvertjob
  WHERE original_object_key = 'test/audio.wav';"

# Expected: Status = 'COMPLETE', processing time ~5 seconds
```

### Video File Testing
```bash
# 1. Upload test video file
aws s3 cp test-video.mp4 s3://your-input-bucket/test/video.mp4

# 2. Check MediaConvert job creation
aws logs tail /aws/lambda/soremo-mediaconvert --follow

# 3. Monitor MediaConvert job
JOB_ID=$(aws logs filter-log-events \
  --log-group-name /aws/lambda/soremo-mediaconvert \
  --filter-pattern "Job ID" \
  --query 'events[0].message' --output text | grep -o 'job-[a-zA-Z0-9]*')

aws mediaconvert get-job --id $JOB_ID --query 'Job.Status'

# 4. Verify HLS output files
aws s3 ls s3://your-output-bucket/test/video-converted.m3u8
aws s3 ls s3://your-output-bucket/test/ --recursive | grep ".ts"

# Expected: Status = 'COMPLETE', HLS files created, processing time ~30-60 seconds
```

### Frontend Integration Testing
```javascript
// Test audio peaks loading
fetch('/api/s3/your-output-bucket/test/audio_peaks.json')
  .then(response => response.json())
  .then(peaksData => {
    console.log('Peaks data:', peaksData);
    // Expected: { version: 2, channels: 1, data: [min1, max1, min2, max2, ...] }
    wavesurfer.backend.setMergedPeaks(peaksData.data);
  });

// Test HLS video loading
const video = document.getElementById('video');
if (Hls.isSupported()) {
  const hls = new Hls();
  hls.loadSource('/api/s3/your-output-bucket/test/video-converted.m3u8');
  hls.attachMedia(video);
}
```

## 📋 Supported Formats & Output

### Audio Files (Direct Processing)
| Format | Extension | audiowaveform Support | Output |
|--------|-----------|----------------------|---------|
| MP3 | `.mp3` | ✅ Native | `file_peaks.json` |
| WAV | `.wav` | ✅ Native | `file_peaks.json` |
| M4A | `.m4a` | ✅ Native | `file_peaks.json` |
| AAC | `.aac` | ✅ Native | `file_peaks.json` |
| FLAC | `.flac` | ✅ Native | `file_peaks.json` |
| OGG | `.ogg` | ✅ Native | `file_peaks.json` |

### Video Files (MediaConvert Processing)
| Format | Extension | MediaConvert Support | Output |
|--------|-----------|---------------------|---------|
| MP4 | `.mp4` | ✅ Native | HLS (`.m3u8` + `.ts`) |
| MOV | `.mov` | ✅ Native | HLS (`.m3u8` + `.ts`) |
| MKV | `.mkv` | ✅ Native | HLS (`.m3u8` + `.ts`) |
| AVI | `.avi` | ✅ Native | HLS (`.m3u8` + `.ts`) |
| WebM | `.webm` | ✅ Native | HLS (`.m3u8` + `.ts`) |
| MPG | `.mpg` | ✅ Native | HLS (`.m3u8` + `.ts`) |

### Output File Structure
```
s3://output-bucket/
├── folder/
│   ├── audio-file_peaks.json          # Audio waveform peaks
│   ├── video-file-converted.m3u8      # HLS playlist
│   ├── video-file-converted000.ts     # HLS segment 1
│   ├── video-file-converted001.ts     # HLS segment 2
│   └── ...                            # Additional segments
```

### Peaks JSON Format
```json
{
  "version": 2,
  "channels": 1,
  "sample_rate": 44100,
  "samples_per_pixel": 512,
  "bits": 8,
  "length": 64,
  "data": [-0.3, 0.5, -0.2, 0.8, -0.1, 0.6, ...]
}
```

## 🔍 Troubleshooting

### Common Issues

#### Audio Processing Fails
```bash
# Check audiowaveform binary
aws lambda invoke --function-name soremo-mediaconvert \
  --payload '{"test": "audiowaveform"}' response.json

# Check supported formats
# Ensure file is not corrupted
# Verify file size < Lambda limits
```

#### Video Processing Fails
```bash
# Check MediaConvert job details
aws mediaconvert get-job --id <job-id> --query 'Job.ErrorMessage'

# Common issues:
# - Unsupported codec
# - File too large
# - Invalid S3 permissions
```

#### Database Connection Issues
```bash
# Check SSM parameter
aws ssm get-parameter --name /soremo/database/credentials --with-decryption

# Verify RDS connectivity
# Check security groups
# Validate credentials format
```

### Performance Optimization

#### Lambda Configuration
```bash
# Optimal settings for media processing
Memory: 3008 MB (maximum)
Timeout: 15 minutes
Architecture: x86_64
Runtime: Container (Node.js 20)
```

#### S3 Optimization
```bash
# Use appropriate storage classes
# Enable transfer acceleration for large files
# Configure lifecycle policies for cleanup
```

### Monitoring & Alerts

#### CloudWatch Metrics
- Lambda duration and errors
- MediaConvert job success/failure rates
- S3 upload/download metrics
- RDS connection metrics

#### Recommended Alarms
```bash
# Lambda errors
aws cloudwatch put-metric-alarm \
  --alarm-name "SoremoMediaConvert-Errors" \
  --alarm-description "Lambda function errors" \
  --metric-name Errors \
  --namespace AWS/Lambda \
  --statistic Sum \
  --period 300 \
  --threshold 1 \
  --comparison-operator GreaterThanOrEqualToThreshold

# MediaConvert failures
aws cloudwatch put-metric-alarm \
  --alarm-name "SoremoMediaConvert-JobFailures" \
  --alarm-description "MediaConvert job failures" \
  --metric-name JobsErrored \
  --namespace AWS/MediaConvert \
  --statistic Sum \
  --period 300 \
  --threshold 1 \
  --comparison-operator GreaterThanOrEqualToThreshold
```

---

## 🎯 Summary

The Soremo MediaConvert Lambda architecture (v2.0) provides:

- **🚀 High Performance**: Fast audio processing (~5s) with direct audiowaveform usage
- **🔧 Simplified Architecture**: Only 2 handlers for complete media processing
- **💰 Cost Effective**: Reduced MediaConvert usage for audio files
- **📊 Better Monitoring**: Comprehensive logging and metrics
- **🛠️ Easy Maintenance**: Clean, well-documented code
- **🔄 Scalable Design**: Handles both audio and video efficiently

This architecture is optimized for the Soremo platform's media processing needs while maintaining high performance, reliability, and cost-effectiveness.