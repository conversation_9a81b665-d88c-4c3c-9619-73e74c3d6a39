# Soremo MediaConvert Lambda Dockerfile
# Multi-stage build for audiowaveform binary and Lambda runtime
# Stage 1: Build audiowaveform with all dependencies
FROM amazonlinux:2023 as builder

WORKDIR /tmp

RUN dnf update -yq && \
    dnf install -yq libicu-devel make cmake3 automake libtool gcc gcc-c++ wget tar \
        gzip zip libcurl-devel zlib-static libpng-static xz git python python-devel \
        bzip2-devel which gd-devel

# Download static ffprobe (detect arch) and place it in a known path
RUN set -eux; \
    ARCH="$(uname -m)"; \
    if [ "$ARCH" = "aarch64" ]; then \
      FF_URL="https://johnvansickle.com/ffmpeg/releases/ffmpeg-release-arm64-static.tar.xz"; \
    elif [ "$ARCH" = "x86_64" ]; then \
      FF_URL="https://johnvansickle.com/ffmpeg/releases/ffmpeg-release-amd64-static.tar.xz"; \
    else \
      echo "Unsupported architecture: $ARCH"; exit 1; \
    fi; \
    wget -O /tmp/ffmpeg-static.tar.xz "$FF_URL"; \
    mkdir -p /tmp/ffmpeg-static; \
    tar -xJf /tmp/ffmpeg-static.tar.xz -C /tmp/ffmpeg-static --strip-components=1; \
    cp /tmp/ffmpeg-static/ffprobe /usr/local/bin/ffprobe; \
    chmod +x /usr/local/bin/ffprobe

RUN wget http://sourceforge.net/projects/mad/files/libid3tag/0.15.1b/libid3tag-0.15.1b.tar.gz && \
    tar xzf libid3tag-0.15.1b.tar.gz && \
    cd libid3tag-0.15.1b && \
    ./configure --enable-static --libdir=/lib64 --build=aarch64-unknown-linux-gnu && \
    make && \
    make install

RUN wget https://netix.dl.sourceforge.net/project/mad/libmad/0.15.1b/libmad-0.15.1b.tar.gz && \
    tar xzf libmad-0.15.1b.tar.gz && \
    cd libmad-0.15.1b && \
    sed -i 's/ -fforce-mem//' configure && \
    ./configure --disable-shared --libdir=/lib64 --build=aarch64-unknown-linux-gnu && \
    make install

RUN wget https://ftp.osuosl.org/pub/xiph/releases/flac/flac-1.3.0.tar.xz && \
    tar xf flac-1.3.0.tar.xz && \
    cd flac-1.3.0 && \
    ./configure --disable-shared --libdir=/lib64 --build=aarch64-unknown-linux-gnu && \
    make install

RUN wget http://downloads.xiph.org/releases/ogg/libogg-1.3.4.tar.gz && \
    tar xf libogg-1.3.4.tar.gz && \
    cd libogg-1.3.4 && \
    ./configure --disable-shared --libdir=/lib64 --build=aarch64-unknown-linux-gnu && \
    make install

RUN wget http://downloads.xiph.org/releases/vorbis/libvorbis-1.3.6.tar.gz && \
    tar xf libvorbis-1.3.6.tar.gz && \
    cd libvorbis-1.3.6 && \
    ./configure --disable-shared --libdir=/lib64 --build=aarch64-unknown-linux-gnu && \
    make install

RUN wget https://archive.mozilla.org/pub/opus/opus-1.3.1.tar.gz && \
    tar xzf opus-1.3.1.tar.gz && \
    cd opus-1.3.1 && \
    ./configure --disable-shared --libdir=/lib64 --build=aarch64-unknown-linux-gnu && \
    make install

RUN git clone https://github.com/libgd/libgd.git && \
    cd libgd && \
    mkdir build && \
    cd $_ && \
    cmake3 -DBUILD_STATIC_LIBS=1 -DENABLE_PNG=1 .. && \
    make && \
    mv Bin/libgd.a /lib64

RUN wget https://github.com/libsndfile/libsndfile/archive/refs/tags/1.1.0.tar.gz && \
    tar xzf 1.1.0.tar.gz && \
    cd libsndfile-1.1.0 && \
    mkdir build && \
    cd build && \
    cmake -D ENABLE_MPEG=0 -D ENABLE_TESTING=0 -D BUILD_SHARED_LIBS=0 -D BUILD_REGTEST=0 -D BUILD_PROGRAMS=0 -D BUILD_TESTING=0 -D BUILD_EXAMPLES=0 .. && \
    make && \
    make install

RUN wget https://archives.boost.io/release/1.85.0/source/boost_1_85_0.tar.gz && \
    tar xzf boost_1_85_0.tar.gz && \
    cd boost_1_85_0 && \
    ./bootstrap.sh --without-icu --libdir=/lib64 --includedir=/usr/include && \
    ./b2 --disable-icu --with-program_options --with-filesystem --with-system --with-regex link=static install && \
    cd .. && rm -rf boost_1_85_0 boost_1_85_0.tar.gz # Cleanup after build

RUN wget https://github.com/bbc/audiowaveform/archive/1.10.1.tar.gz && \
    tar xzf 1.10.1.tar.gz && \
    cd ./audiowaveform-1.10.1 && \
    mkdir ./build && \
    cd ./build && \
    cmake3 -D ENABLE_TESTS=0 -D CMAKE_BUILD_TYPE=Release -D BUILD_STATIC=1 .. && \
    cmake --build . --config Release


# Stage 2: Final Lambda runtime with audiowaveform binary
FROM public.ecr.aws/lambda/nodejs:20

# Copy audiowaveform binary from builder stage
COPY --from=builder /tmp/audiowaveform-1.10.1/build/audiowaveform /usr/local/bin/audiowaveform

# Copy ffprobe binary from builder stage
COPY --from=builder /usr/local/bin/ffprobe /usr/local/bin/ffprobe

# Copy package.json and install dependencies
COPY package*.json ./
RUN npm ci --only=production && \
    npm cache clean --force && \
    rm -rf /tmp/* /var/tmp/*

# Copy application code
COPY index.js ./

# Set the CMD to your handler (could also be done as a parameter override outside of the Dockerfile)
CMD [ "index.startConversionHandler" ]